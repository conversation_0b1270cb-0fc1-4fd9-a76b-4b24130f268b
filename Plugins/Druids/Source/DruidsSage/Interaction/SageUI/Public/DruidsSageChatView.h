#pragma once

#include <CoreMinimal.h>
#include "Blueprint/UserWidget.h"

#include "IDruidsSageChatItem.h"
#include "DruidsSageChatTypes.h"

// Forward declarations
class UDruidsSageMultiLineTextInput;
class UContextChipWidget;

class ISageExtensionDelegator;
struct FDruidsSageExtensionDefinition;
class IChatRequestHandler;

class UDruidsSageChatRequest_v2;

#include "DruidsSageChatView.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnMessageSending);

/**
 * UMG Chat View widget for DruidsSage
 * Converted from SDruidsSageChatView Slate widget
 */
UCLASS(meta = (DisplayName = "Druids Sage Chat View"))
class SAGEUI_API UDruidsSageChatView : public UUserWidget
{
	GENERATED_BODY()

public:
	UDruidsSageChatView(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void SynchronizeProperties() override;
	virtual void ReleaseSlateResources(bool bReleaseChildren) override;
	// End of UUserWidget interface

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Chat View")
	FOnMessageSending OnMessageSending;

	// Public API
	UFUNCTION(BlueprintCallable, Category = "Chat View")
	bool IsSendMessageEnabled() const;

	UFUNCTION(BlueprintCallable, Category = "Chat View")
	bool IsClearChatEnabled() const;

	UFUNCTION(BlueprintCallable, Category = "Chat View")
	void ClearChat();

	void SetTabContext(const FString& Context, const FString& ContextDisplayMessage);
	void SetBPContext(const FString& BPContext, const FString& BPContextDisplayMessage);
	void SetActiveObject(const TWeakObjectPtr<>& NewActiveObject);
	void SetActiveExtensionDefinitions(const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& NewActiveExtensions);
	void SetChatRequestHandler(const TWeakPtr<IChatRequestHandler>& NewChatRequestHandler);
	void SetExtensionsDelegator(const TWeakPtr<ISageExtensionDelegator>& NewExtensionDelegator);

	// Focus management
	void SetInputFocus();

protected:
	// BindWidget properties for Blueprint binding
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	UContextChipWidget* BPContextChipWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	UContextChipWidget* TabContextChipWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	UDruidsSageMultiLineTextInput* InputTextBox;

	// Button widgets for send and clear functionality
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	class UButton* SendButton;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat View")
	class UButton* ClearButton;

	// Optional BindWidget for chat content area - if not bound, will create Slate widgets at runtime
	UPROPERTY(meta = (BindWidget, OptionalWidget = true), BlueprintReadOnly, Category = "Chat View")
	class UPanelWidget* ChatContentPanel;

private:
	TSharedPtr<IChatRequestHandler> ChatRequestHandler;

	// Chat functionality
	void HandleSendMessage(const EDruidsSageChatRole Role);
	void HandleClearChat();

	UFUNCTION()
	void HandleEnterPressed();

	UFUNCTION()
	void HandleSendButtonClicked();

	UFUNCTION()
	void HandleClearButtonClicked();

	TArray<FDruidsSageChatMessage> GetChatHistory() const;
	void LoadChatHistory();
	void SaveChatHistory() const;
	FString GetHistoryPath() const;

	// Tab/General Context
	FString CurrentTabContext;
	FString CurrentTabContextDisplayMessage;

	// Blueprint Context
	FString CurrentBPContext;
	FString CurrentBPContextDisplayMessage;

	// Slate widgets for chat content (until chat items are converted to UMG)
	TSharedPtr<SVerticalBox> ChatBox;
	TSharedPtr<SScrollBox> ChatScrollBox;
	TArray<TSharedPtr<IDruidsSageChatItem>> ChatItems;

	TWeakObjectPtr<> ActiveObject;

	// Helper for truncation
	FString TruncateString(const FString& Input, int32 MaxLength = 100) const;

	TArray<TSharedPtr<FDruidsSageExtensionDefinition>> ActiveExtensionDefinitions;
	TSharedPtr<ISageExtensionDelegator> ExtensionDelegator;

	TSharedPtr<IDruidsSageChatItem> CreateChatItem(
		EDruidsSageChatRole Role,
		const FString& ChatText = FString()) const;

	void OnActionRequestApplied(const TSharedPtr<FJsonValue>& ActionDetails) const;

	// Initialize chat content area (either in UMG panel or create Slate widgets)
	void InitializeChatContent();

	// Flag to track if this is a live runtime instance (vs design-time/compilation instance)
	bool bIsLiveInstance;
};
